../Middlewares/Third_Party/FreeRTOS/Source/queue.c:247:12:xQueueGenericReset	24	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/queue.c:302:16:xQueueGenericCreateStatic	48	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/queue.c:358:16:xQueueGenericCreate	56	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/queue.c:408:13:prvInitialiseNewQueue	24	static
../Middlewares/Third_Party/FreeRTOS/Source/queue.c:452:14:prvInitialiseMutex	16	static
../Middlewares/Third_Party/FreeRTOS/Source/queue.c:482:16:xQueueCreateMutex	32	static
../Middlewares/Third_Party/FreeRTOS/Source/queue.c:498:16:xQueueCreateMutexStatic	40	static
../Middlewares/Third_Party/FreeRTOS/Source/queue.c:518:8:xQueueGetMutexHolder	24	static
../Middlewares/Third_Party/FreeRTOS/Source/queue.c:548:8:xQueueGetMutexHolderFromISR	24	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/queue.c:574:13:xQueueGiveMutexRecursive	32	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/queue.c:629:13:xQueueTakeMutexRecursive	32	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/queue.c:671:16:xQueueCreateCountingSemaphoreStatic	40	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/queue.c:699:16:xQueueCreateCountingSemaphore	24	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/queue.c:725:12:xQueueGenericSend	48	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/queue.c:923:12:xQueueGenericSendFromISR	48	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/queue.c:1074:12:xQueueGiveFromISR	40	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/queue.c:1239:12:xQueueReceive	48	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/queue.c:1380:12:xQueueSemaphoreTake	48	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/queue.c:1599:12:xQueuePeek	48	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/queue.c:1749:12:xQueueReceiveFromISR	56	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/queue.c:1840:12:xQueuePeekFromISR	32	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/queue.c:1894:13:uxQueueMessagesWaiting	24	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/queue.c:1910:13:uxQueueSpacesAvailable	24	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/queue.c:1928:13:uxQueueMessagesWaitingFromISR	24	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/queue.c:1940:6:vQueueDelete	24	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/queue.c:1984:14:uxQueueGetQueueNumber	16	static
../Middlewares/Third_Party/FreeRTOS/Source/queue.c:1994:7:vQueueSetQueueNumber	16	static
../Middlewares/Third_Party/FreeRTOS/Source/queue.c:2004:10:ucQueueGetQueueType	16	static
../Middlewares/Third_Party/FreeRTOS/Source/queue.c:2014:21:prvGetDisinheritPriorityAfterTimeout	24	static
../Middlewares/Third_Party/FreeRTOS/Source/queue.c:2039:19:prvCopyDataToQueue	32	static
../Middlewares/Third_Party/FreeRTOS/Source/queue.c:2118:13:prvCopyDataFromQueue	16	static
../Middlewares/Third_Party/FreeRTOS/Source/queue.c:2136:13:prvUnlockQueue	24	static
../Middlewares/Third_Party/FreeRTOS/Source/queue.c:2256:19:prvIsQueueEmpty	24	static
../Middlewares/Third_Party/FreeRTOS/Source/queue.c:2277:12:xQueueIsQueueEmptyFromISR	24	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/queue.c:2295:19:prvIsQueueFull	24	static
../Middlewares/Third_Party/FreeRTOS/Source/queue.c:2316:12:xQueueIsQueueFullFromISR	24	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/queue.c:2611:7:vQueueAddToRegistry	24	static
../Middlewares/Third_Party/FreeRTOS/Source/queue.c:2640:14:pcQueueGetName	24	static
../Middlewares/Third_Party/FreeRTOS/Source/queue.c:2668:7:vQueueUnregisterQueue	24	static
../Middlewares/Third_Party/FreeRTOS/Source/queue.c:2700:7:vQueueWaitForMessageRestricted	32	static
