../Middlewares/Third_Party/FreeRTOS/Source/timers.c:223:12:xTimerCreateTimerTask	48	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/timers.c:278:16:xTimer<PERSON>reate	48	static
../Middlewares/Third_Party/FreeRTOS/Source/timers.c:310:16:xTimerCreateStatic	48	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/timers.c:352:13:prvInitialiseNewTimer	24	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/timers.c:381:12:xTimerGenericCommand	56	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/timers.c:424:14:xTimerGetTimerDaemonTaskHandle	8	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/timers.c:433:12:xTimerGetPeriod	24	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/timers.c:442:12:xTimerGetExpiryTime	24	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/timers.c:453:14:pcTimerGetName	24	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/timers.c:462:13:prvProcessExpiredTimer	32	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/timers.c:502:13:prvTimerTask	24	static
../Middlewares/Third_Party/FreeRTOS/Source/timers.c:538:13:prvProcessTimerOrBlockTask	24	static
../Middlewares/Third_Party/FreeRTOS/Source/timers.c:598:19:prvGetNextExpireTime	24	static
../Middlewares/Third_Party/FreeRTOS/Source/timers.c:624:19:prvSampleTimeNow	24	static
../Middlewares/Third_Party/FreeRTOS/Source/timers.c:647:19:prvInsertTimerInActiveList	32	static
../Middlewares/Third_Party/FreeRTOS/Source/timers.c:688:13:prvProcessReceivedCommands	64	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/timers.c:835:13:prvSwitchTimerLists	40	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/timers.c:894:13:prvCheckForValidListAndQueue	16	static
../Middlewares/Third_Party/FreeRTOS/Source/timers.c:945:12:xTimerIsTimerActive	24	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/timers.c:966:7:pvTimerGetTimerID	24	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/timers.c:983:6:vTimerSetTimerID	24	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/timers.c:999:13:xTimerPendFunctionCallFromISR	48	static
../Middlewares/Third_Party/FreeRTOS/Source/timers.c:1023:13:xTimerPendFunctionCall	48	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/timers.c:1052:14:uxTimerGetTimerNumber	16	static
../Middlewares/Third_Party/FreeRTOS/Source/timers.c:1062:7:vTimerSetTimerNumber	16	static
