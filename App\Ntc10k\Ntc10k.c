#include "Ntc10k.h"
#include<math.h>

float NTC_Read_Resistor(int ADC_input, int ADC_range_bit, int RES_attend_value, char NTC_Up_Down)
{
	float ADC_NTC_Res, ADC_Res_attend, NTC_Resistor;
	if (NTC_Up_Down == 0)
	{ 
		ADC_Res_attend = ADC_input;
		ADC_NTC_Res = pow(2,ADC_range_bit)-1-ADC_input;
		
	}
	else 
	{
		ADC_NTC_Res = ADC_input;
		ADC_Res_attend = pow(2,ADC_range_bit)-1-ADC_input;
	}
	NTC_Resistor = (ADC_Res_attend * RES_attend_value) / ADC_NTC_Res;
	return NTC_Resistor;
}

float NTC_Calculate(float RES_current, float RES_25C, float Beta) // RES_25C = 10k, Beta = 3380 or 3950
{
	float T = 1/(1/298.15+(log(RES_current/RES_25C)/Beta))-273.15;
	return T;
}