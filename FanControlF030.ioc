#MicroXplorer Configuration settings - do not modify
ADC.IPParameters=SamplingTime
ADC.SamplingTime=ADC_SAMPLETIME_28CYCLES_5
CAD.formats=
CAD.pinconfig=
CAD.provider=
Dma.Request0=TIM3_CH1/TRIG
Dma.Request1=TIM3_CH3
Dma.Request2=TIM3_CH4/UP
Dma.Request3=TIM17_CH1/UP
Dma.RequestsNb=4
Dma.TIM17_CH1/UP.3.Direction=DMA_PERIPH_TO_MEMORY
Dma.TIM17_CH1/UP.3.Instance=DMA1_Channel1
Dma.TIM17_CH1/UP.3.MemDataAlignment=DMA_MDATAALIGN_WORD
Dma.TIM17_CH1/UP.3.MemInc=DMA_MINC_ENABLE
Dma.TIM17_CH1/UP.3.Mode=DMA_NORMAL
Dma.TIM17_CH1/UP.3.PeriphDataAlignment=DMA_PDATAALIGN_HALFWORD
Dma.TIM17_CH1/UP.3.PeriphInc=DMA_PINC_DISABLE
Dma.TIM17_CH1/UP.3.Priority=DMA_PRIORITY_LOW
Dma.TIM17_CH1/UP.3.RequestParameters=Instance,Direction,PeriphInc,MemInc,PeriphDataAlignment,MemDataAlignment,Mode,Priority
Dma.TIM3_CH1/TRIG.0.Direction=DMA_PERIPH_TO_MEMORY
Dma.TIM3_CH1/TRIG.0.Instance=DMA1_Channel4
Dma.TIM3_CH1/TRIG.0.MemDataAlignment=DMA_MDATAALIGN_WORD
Dma.TIM3_CH1/TRIG.0.MemInc=DMA_MINC_ENABLE
Dma.TIM3_CH1/TRIG.0.Mode=DMA_NORMAL
Dma.TIM3_CH1/TRIG.0.PeriphDataAlignment=DMA_PDATAALIGN_HALFWORD
Dma.TIM3_CH1/TRIG.0.PeriphInc=DMA_PINC_DISABLE
Dma.TIM3_CH1/TRIG.0.Priority=DMA_PRIORITY_LOW
Dma.TIM3_CH1/TRIG.0.RequestParameters=Instance,Direction,PeriphInc,MemInc,PeriphDataAlignment,MemDataAlignment,Mode,Priority
Dma.TIM3_CH3.1.Direction=DMA_PERIPH_TO_MEMORY
Dma.TIM3_CH3.1.Instance=DMA1_Channel2
Dma.TIM3_CH3.1.MemDataAlignment=DMA_MDATAALIGN_WORD
Dma.TIM3_CH3.1.MemInc=DMA_MINC_ENABLE
Dma.TIM3_CH3.1.Mode=DMA_NORMAL
Dma.TIM3_CH3.1.PeriphDataAlignment=DMA_PDATAALIGN_HALFWORD
Dma.TIM3_CH3.1.PeriphInc=DMA_PINC_DISABLE
Dma.TIM3_CH3.1.Priority=DMA_PRIORITY_LOW
Dma.TIM3_CH3.1.RequestParameters=Instance,Direction,PeriphInc,MemInc,PeriphDataAlignment,MemDataAlignment,Mode,Priority
Dma.TIM3_CH4/UP.2.Direction=DMA_PERIPH_TO_MEMORY
Dma.TIM3_CH4/UP.2.Instance=DMA1_Channel3
Dma.TIM3_CH4/UP.2.MemDataAlignment=DMA_MDATAALIGN_WORD
Dma.TIM3_CH4/UP.2.MemInc=DMA_MINC_ENABLE
Dma.TIM3_CH4/UP.2.Mode=DMA_NORMAL
Dma.TIM3_CH4/UP.2.PeriphDataAlignment=DMA_PDATAALIGN_HALFWORD
Dma.TIM3_CH4/UP.2.PeriphInc=DMA_PINC_DISABLE
Dma.TIM3_CH4/UP.2.Priority=DMA_PRIORITY_LOW
Dma.TIM3_CH4/UP.2.RequestParameters=Instance,Direction,PeriphInc,MemInc,PeriphDataAlignment,MemDataAlignment,Mode,Priority
File.Version=6
GPIO.groupedBy=Group By Peripherals
IWDG.IPParameters=Prescaler
IWDG.Prescaler=IWDG_PRESCALER_256
KeepUserPlacement=false
Mcu.CPN=STM32F030C8T6
Mcu.Family=STM32F0
Mcu.IP0=ADC
Mcu.IP1=DMA
Mcu.IP10=USART2
Mcu.IP2=IWDG
Mcu.IP3=NVIC
Mcu.IP4=RCC
Mcu.IP5=SYS
Mcu.IP6=TIM1
Mcu.IP7=TIM3
Mcu.IP8=TIM17
Mcu.IP9=USART1
Mcu.IPNb=11
Mcu.Name=STM32F030C8Tx
Mcu.Package=LQFP48
Mcu.Pin0=PC13
Mcu.Pin1=PA0
Mcu.Pin10=PB1
Mcu.Pin11=PB12
Mcu.Pin12=PB13
Mcu.Pin13=PB14
Mcu.Pin14=PB15
Mcu.Pin15=PA8
Mcu.Pin16=PA9
Mcu.Pin17=PA10
Mcu.Pin18=PA11
Mcu.Pin19=PA13
Mcu.Pin2=PA1
Mcu.Pin20=PA14
Mcu.Pin21=PB5
Mcu.Pin22=PB6
Mcu.Pin23=PB7
Mcu.Pin24=VP_IWDG_VS_IWDG
Mcu.Pin25=VP_SYS_VS_Systick
Mcu.Pin26=VP_TIM3_VS_ClockSourceINT
Mcu.Pin27=VP_TIM17_VS_ClockSourceINT
Mcu.Pin3=PA2
Mcu.Pin4=PA3
Mcu.Pin5=PA4
Mcu.Pin6=PA5
Mcu.Pin7=PA6
Mcu.Pin8=PA7
Mcu.Pin9=PB0
Mcu.PinsNb=28
Mcu.ThirdPartyNb=0
Mcu.UserConstants=
Mcu.UserName=STM32F030C8Tx
MxCube.Version=6.14.1
MxDb.Version=DB.6.0.141
NVIC.ADC1_IRQn=true\:0\:0\:false\:false\:true\:true\:true\:true
NVIC.DMA1_Channel1_IRQn=true\:0\:0\:false\:false\:true\:false\:true\:true
NVIC.DMA1_Channel2_3_IRQn=true\:0\:0\:false\:false\:true\:false\:true\:true
NVIC.DMA1_Channel4_5_IRQn=true\:0\:0\:false\:false\:true\:false\:true\:true
NVIC.ForceEnableDMAVector=true
NVIC.HardFault_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.NonMaskableInt_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.PendSV_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.SVC_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:true
NVIC.SysTick_IRQn=true\:3\:0\:false\:false\:true\:false\:true\:false
PA0.GPIOParameters=GPIO_Label
PA0.GPIO_Label=ADC_OUT
PA0.Locked=true
PA0.Signal=GPIO_Output
PA1.Mode=IN1
PA1.Signal=ADC_IN1
PA10.Signal=S_TIM1_CH3
PA11.Signal=S_TIM1_CH4
PA13.Mode=Serial_Wire
PA13.Signal=SYS_SWDIO
PA14.Locked=true
PA14.Mode=Serial_Wire
PA14.Signal=SYS_SWCLK
PA2.Mode=Asynchronous
PA2.Signal=USART2_TX
PA3.Mode=Asynchronous
PA3.Signal=USART2_RX
PA4.GPIOParameters=GPIO_Label
PA4.GPIO_Label=LED_485
PA4.Locked=true
PA4.Signal=GPIO_Output
PA5.GPIOParameters=GPIO_Label
PA5.GPIO_Label=IN_1
PA5.Locked=true
PA5.Signal=GPIO_Input
PA6.Locked=true
PA6.Signal=S_TIM3_CH1
PA7.Locked=true
PA7.Signal=S_TIM17_CH1
PA8.Signal=S_TIM1_CH1
PA9.Signal=S_TIM1_CH2
PB0.Signal=S_TIM3_CH3
PB1.Signal=S_TIM3_CH4
PB12.GPIOParameters=GPIO_Label
PB12.GPIO_Label=LED_F4
PB12.Locked=true
PB12.Signal=GPIO_Output
PB13.GPIOParameters=GPIO_Label
PB13.GPIO_Label=LED_F3
PB13.Locked=true
PB13.Signal=GPXTI13
PB14.GPIOParameters=GPIO_Label
PB14.GPIO_Label=LED_F2
PB14.Locked=true
PB14.Signal=GPIO_Output
PB15.GPIOParameters=GPIO_Label
PB15.GPIO_Label=LED_F1
PB15.Locked=true
PB15.Signal=GPIO_Output
PB5.GPIOParameters=GPIO_Label
PB5.GPIO_Label=USART1_EN
PB5.Locked=true
PB5.Signal=GPIO_Output
PB6.Mode=Asynchronous
PB6.Signal=USART1_TX
PB7.Mode=Asynchronous
PB7.Signal=USART1_RX
PC13.GPIOParameters=GPIO_Label
PC13.GPIO_Label=LED1
PC13.Locked=true
PC13.Signal=GPIO_Output
PinOutPanel.RotationAngle=0
ProjectManager.AskForMigrate=true
ProjectManager.BackupPrevious=false
ProjectManager.CompilerLinker=GCC
ProjectManager.CompilerOptimize=6
ProjectManager.ComputerToolchain=false
ProjectManager.CoupleFile=true
ProjectManager.CustomerFirmwarePackage=
ProjectManager.DefaultFWLocation=true
ProjectManager.DeletePrevious=true
ProjectManager.DeviceId=STM32F030C8Tx
ProjectManager.FirmwarePackage=STM32Cube FW_F0 V1.11.5
ProjectManager.FreePins=false
ProjectManager.HalAssertFull=false
ProjectManager.HeapSize=0x200
ProjectManager.KeepUserCode=true
ProjectManager.LastFirmware=true
ProjectManager.LibraryCopy=1
ProjectManager.MainLocation=Core/Src
ProjectManager.NoMain=false
ProjectManager.PreviousToolchain=STM32CubeIDE
ProjectManager.ProjectBuild=false
ProjectManager.ProjectFileName=FanControlF030.ioc
ProjectManager.ProjectName=FanControlF030
ProjectManager.ProjectStructure=
ProjectManager.RegisterCallBack=
ProjectManager.StackSize=0x400
ProjectManager.TargetToolchain=STM32CubeIDE
ProjectManager.ToolChainLocation=
ProjectManager.UAScriptAfterPath=
ProjectManager.UAScriptBeforePath=
ProjectManager.UnderRoot=true
ProjectManager.functionlistsort=1-SystemClock_Config-RCC-false-HAL-false,2-MX_GPIO_Init-GPIO-false-HAL-true,3-MX_DMA_Init-DMA-false-HAL-true,4-MX_TIM1_Init-TIM1-false-HAL-true,5-MX_TIM3_Init-TIM3-false-HAL-true,6-MX_USART1_UART_Init-USART1-false-HAL-true,7-MX_ADC_Init-ADC-false-HAL-true,8-MX_TIM17_Init-TIM17-false-HAL-true,9-MX_USART2_UART_Init-USART2-false-HAL-true,10-MX_IWDG_Init-IWDG-false-HAL-true
RCC.AHBFreq_Value=48000000
RCC.APB1Freq_Value=48000000
RCC.APB1TimFreq_Value=48000000
RCC.FCLKCortexFreq_Value=48000000
RCC.FamilyName=M
RCC.HCLKFreq_Value=48000000
RCC.IPParameters=AHBFreq_Value,APB1Freq_Value,APB1TimFreq_Value,FCLKCortexFreq_Value,FamilyName,HCLKFreq_Value,MCOFreq_Value,PLLCLKFreq_Value,PLLMCOFreq_Value,PLLMUL,SYSCLKFreq_VALUE,SYSCLKSource,TimSysFreq_Value,USART1Freq_Value
RCC.MCOFreq_Value=48000000
RCC.PLLCLKFreq_Value=48000000
RCC.PLLMCOFreq_Value=24000000
RCC.PLLMUL=RCC_PLL_MUL12
RCC.SYSCLKFreq_VALUE=48000000
RCC.SYSCLKSource=RCC_SYSCLKSOURCE_PLLCLK
RCC.TimSysFreq_Value=48000000
RCC.USART1Freq_Value=48000000
SH.GPXTI13.0=GPIO_EXTI13
SH.GPXTI13.ConfNb=1
SH.S_TIM17_CH1.0=TIM17_CH1,Input_Capture1_from_TI1
SH.S_TIM17_CH1.ConfNb=1
SH.S_TIM1_CH1.0=TIM1_CH1,PWM Generation1 CH1
SH.S_TIM1_CH1.ConfNb=1
SH.S_TIM1_CH2.0=TIM1_CH2,PWM Generation2 CH2
SH.S_TIM1_CH2.ConfNb=1
SH.S_TIM1_CH3.0=TIM1_CH3,PWM Generation3 CH3
SH.S_TIM1_CH3.ConfNb=1
SH.S_TIM1_CH4.0=TIM1_CH4,PWM Generation4 CH4
SH.S_TIM1_CH4.ConfNb=1
SH.S_TIM3_CH1.0=TIM3_CH1,Input_Capture1_from_TI1
SH.S_TIM3_CH1.ConfNb=1
SH.S_TIM3_CH3.0=TIM3_CH3,Input_Capture3_from_TI3
SH.S_TIM3_CH3.ConfNb=1
SH.S_TIM3_CH4.0=TIM3_CH4,Input_Capture4_from_TI4
SH.S_TIM3_CH4.ConfNb=1
TIM1.Channel-PWM\ Generation1\ CH1=TIM_CHANNEL_1
TIM1.Channel-PWM\ Generation2\ CH2=TIM_CHANNEL_2
TIM1.Channel-PWM\ Generation3\ CH3=TIM_CHANNEL_3
TIM1.Channel-PWM\ Generation4\ CH4=TIM_CHANNEL_4
TIM1.IPParameters=Channel-PWM Generation1 CH1,Channel-PWM Generation2 CH2,Channel-PWM Generation3 CH3,Channel-PWM Generation4 CH4,Period
TIM1.Period=1280
TIM17.Channel=TIM_CHANNEL_1
TIM17.ClockDivision=TIM_CLOCKDIVISION_DIV4
TIM17.IPParameters=Channel,Prescaler,ClockDivision
TIM17.Prescaler=16
TIM3.Channel-Input_Capture1_from_TI1=TIM_CHANNEL_1
TIM3.Channel-Input_Capture3_from_TI3=TIM_CHANNEL_3
TIM3.Channel-Input_Capture4_from_TI4=TIM_CHANNEL_4
TIM3.ClockDivision=TIM_CLOCKDIVISION_DIV4
TIM3.IPParameters=Channel-Input_Capture1_from_TI1,Channel-Input_Capture3_from_TI3,Channel-Input_Capture4_from_TI4,Prescaler,ClockDivision
TIM3.Prescaler=16
USART1.BaudRate=9600
USART1.IPParameters=VirtualMode-Asynchronous,BaudRate
USART1.VirtualMode-Asynchronous=VM_ASYNC
USART2.BaudRate=115200
USART2.IPParameters=VirtualMode-Asynchronous,BaudRate
USART2.VirtualMode-Asynchronous=VM_ASYNC
VP_IWDG_VS_IWDG.Mode=IWDG_Activate
VP_IWDG_VS_IWDG.Signal=IWDG_VS_IWDG
VP_SYS_VS_Systick.Mode=SysTick
VP_SYS_VS_Systick.Signal=SYS_VS_Systick
VP_TIM17_VS_ClockSourceINT.Mode=Enable_Timer
VP_TIM17_VS_ClockSourceINT.Signal=TIM17_VS_ClockSourceINT
VP_TIM3_VS_ClockSourceINT.Mode=Internal
VP_TIM3_VS_ClockSourceINT.Signal=TIM3_VS_ClockSourceINT
board=custom
