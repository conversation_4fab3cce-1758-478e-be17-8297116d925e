../Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM0/port.c:133:14:pxPortInitialiseStack	24	static
../Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM0/port.c:151:13:prvTaskExitError	16	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM0/port.c:176:6:<PERSON><PERSON>_Handler	8	static
../Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM0/port.c:183:6:vPortStartFirstTask	0	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM0/port.c:214:12:xPortStartScheduler	8	static
../Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM0/port.c:244:6:vPortEndScheduler	8	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM0/port.c:252:6:vPortYield	8	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM0/port.c:264:6:vPortEnterCritical	8	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM0/port.c:273:6:vPortExitCritical	8	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM0/port.c:284:10:ulSetInterruptMaskFromISR	0	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM0/port.c:302:6:vClearInterruptMaskFromISR	0	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM0/port.c:319:6:PendSV_Handler	0	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM0/port.c:368:6:xPortSysTickHandler	16	static
../Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM0/port.c:541:6:prvSetupTimerInterrupt	8	static
