# Hệ thống điều khiển PWM bằng biến trở

## <PERSON><PERSON> tả chức năng

Dự án này thực hiện điều khiển PWM trên STM32F030 với các chức năng sau:

### 1. Cấp nguồn cho biến trở (PA0)
- **Chân PA0** được cấu hình là output và xuất mức cao (3.3V)
- Cung cấp nguồn cho biến trở để tạo tín hiệu điện áp thay đổi

### 2. Đọc tín hiệu từ biến trở (PA1)
- **Chân PA1** được cấu hình là ADC input (ADC_IN1)
- Đọc điện áp từ biến trở với độ phân giải 12-bit (0-4095)
- Tần suất lấy mẫu được điều khiển bởi interrupt ADC

### 3. <PERSON><PERSON><PERSON><PERSON> khiển PWM (PA9)
- **<PERSON>ân PA9** đư<PERSON><PERSON> cấu hình là TIM1_CH2 (PWM output)
- Duty cycle PWM được điều khiển trực tiếp bởi giá trị biến trở
- Tần số PWM: 25kHz (Period = 1280)

## Cấu hình phần cứng

```
PA0 (ADC_OUT) -----> VCC của biến trở
PA1 (ADC_IN1) -----> Chân giữa của biến trở  
GND           -----> GND của biến trở
PA9 (TIM1_CH2)-----> Output PWM
```

## Thuật toán hoạt động

### Chuyển đổi ADC sang PWM:
```c
PWM_Duty_Cycle = (ADC_Value * 1280) / 4095
```

- ADC_Value = 0    → PWM_Duty = 0    (0% duty cycle)
- ADC_Value = 2047 → PWM_Duty = 640  (50% duty cycle) 
- ADC_Value = 4095 → PWM_Duty = 1280 (100% duty cycle)

### Tần số cập nhật:
- ADC được trigger mỗi 1 giây trong vòng lặp chính
- PWM được cập nhật ngay lập tức sau khi có giá trị ADC mới

## Các file đã chỉnh sửa

### 1. Core/Src/main.c
- Thêm biến `potentiometer_value` và `pwm_duty_cycle`
- Thêm hàm `ADC_to_PWM_DutyCycle()`
- Cập nhật vòng lặp chính để đọc ADC và điều khiển PWM
- Loại bỏ TIM_CHANNEL_2 khỏi điều khiển nhiệt độ tự động

### 2. Core/Src/stm32f0xx_it.c  
- Thêm khai báo biến `CountLed`
- Thêm extern cho `PinIn_State`

### 3. Core/Inc/main.h
- Thêm khai báo hàm `ADC_to_PWM_DutyCycle()`
- Thêm extern cho các biến PWM

## Chức năng debug

Hệ thống gửi thông tin debug qua UART2 với format:
```
Potentiometer: [ADC_Value], PWM_Duty: [Duty_Cycle], Temp: [Temperature]
```

## Lưu ý quan trọng

1. **PA9 (TIM1_CH2)** chỉ được điều khiển bởi biến trở, không bị ảnh hưởng bởi hệ thống điều khiển nhiệt độ tự động
2. Các kênh PWM khác (PA8, PA10, PA11) vẫn hoạt động theo logic điều khiển nhiệt độ ban đầu
3. Biến trở cần được kết nối đúng để tránh short circuit
4. Giá trị PWM được cập nhật real-time theo vị trí biến trở

## Test và kiểm tra

1. Xoay biến trở từ 0% đến 100%
2. Quan sát PWM output trên PA9 bằng oscilloscope
3. Kiểm tra thông tin debug qua UART
4. Xác nhận các kênh PWM khác không bị ảnh hưởng
