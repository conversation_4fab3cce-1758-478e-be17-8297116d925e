# Điều khiển PWM đơn giản bằng biến trở

## Mô tả chức năng

Dự án đơn giản: đọc ADC từ biến trở và điều khiển PWM

### 1. Cấp nguồn cho biến trở (PA0)
- **PA0** xuất mức cao (3.3V) để cấp nguồn cho biến trở

### 2. Đọc ADC từ biến trở (PA1)
- **PA1** đọc điện áp từ biến trở (0-3.3V → 0-4095 ADC)

### 3. Điều khiển PWM (PA9)
- **PA9** xuất PWM với duty cycle tỷ lệ với giá trị ADC
- Tần số PWM: 25kHz

## Kết nối phần cứng

```
PA0 -----> VCC biến trở (3.3V)
PA1 -----> Signal biến trở (ADC input)
GND -----> GND biến trở
PA9 -----> PWM Output
```

## Thuật toán

```c
PWM_Duty = (ADC_Value * 1280) / 4095
```

- ADC = 0    → PWM = 0    (0%)
- ADC = 2047 → PWM = 640  (50%)
- ADC = 4095 → PWM = 1280 (100%)

## Code chính

### Core/Src/main.c
```c
// Đọc ADC và điều khiển PWM
potentiometer_value = ADC_raw;
pwm_duty_cycle = ADC_to_PWM_DutyCycle(potentiometer_value);
__HAL_TIM_SET_COMPARE(&htim1, TIM_CHANNEL_2, pwm_duty_cycle);
```

## Debug qua UART2
```
ADC: [value], PWM: [duty_cycle]
```

## Test
1. Kết nối biến trở
2. Xoay biến trở 0% → 100%
3. Quan sát PWM trên PA9
4. Kiểm tra UART debug
