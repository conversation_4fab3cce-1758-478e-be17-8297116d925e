../Middlewares/Third_Party/FreeRTOS/Source/queue.c:247:12:xQueueGenericReset	5
../Middlewares/Third_Party/FreeRTOS/Source/queue.c:302:16:xQueueGenericCreateStatic	11
../Middlewares/Third_Party/FreeRTOS/Source/queue.c:358:16:xQueueGenericCreate	4
../Middlewares/Third_Party/FreeRTOS/Source/queue.c:408:13:prvInitialiseNewQueue	2
../Middlewares/Third_Party/FreeRTOS/Source/queue.c:452:14:prvInitialiseMutex	2
../Middlewares/Third_Party/FreeRTOS/Source/queue.c:482:16:xQueueCreateMutex	1
../Middlewares/Third_Party/FreeRTOS/Source/queue.c:498:16:xQueueCreateMutexStatic	1
../Middlewares/Third_Party/FreeRTOS/Source/queue.c:518:8:xQueueGetMutexHolder	2
../Middlewares/Third_Party/FreeRTOS/Source/queue.c:548:8:xQueueGetMutexHolderFromISR	3
../Middlewares/Third_Party/FreeRTOS/Source/queue.c:574:13:xQueueGiveMutexRecursive	4
../Middlewares/Third_Party/FreeRTOS/Source/queue.c:629:13:xQueueTakeMutexRecursive	4
../Middlewares/Third_Party/FreeRTOS/Source/queue.c:671:16:xQueueCreateCountingSemaphoreStatic	4
../Middlewares/Third_Party/FreeRTOS/Source/queue.c:699:16:xQueueCreateCountingSemaphore	4
../Middlewares/Third_Party/FreeRTOS/Source/queue.c:725:12:xQueueGenericSend	23
../Middlewares/Third_Party/FreeRTOS/Source/queue.c:923:12:xQueueGenericSendFromISR	14
../Middlewares/Third_Party/FreeRTOS/Source/queue.c:1074:12:xQueueGiveFromISR	11
../Middlewares/Third_Party/FreeRTOS/Source/queue.c:1239:12:xQueueReceive	19
../Middlewares/Third_Party/FreeRTOS/Source/queue.c:1380:12:xQueueSemaphoreTake	21
../Middlewares/Third_Party/FreeRTOS/Source/queue.c:1599:12:xQueuePeek	19
../Middlewares/Third_Party/FreeRTOS/Source/queue.c:1749:12:xQueueReceiveFromISR	10
../Middlewares/Third_Party/FreeRTOS/Source/queue.c:1840:12:xQueuePeekFromISR	7
../Middlewares/Third_Party/FreeRTOS/Source/queue.c:1894:13:uxQueueMessagesWaiting	2
../Middlewares/Third_Party/FreeRTOS/Source/queue.c:1910:13:uxQueueSpacesAvailable	2
../Middlewares/Third_Party/FreeRTOS/Source/queue.c:1928:13:uxQueueMessagesWaitingFromISR	2
../Middlewares/Third_Party/FreeRTOS/Source/queue.c:1940:6:vQueueDelete	3
../Middlewares/Third_Party/FreeRTOS/Source/queue.c:1984:14:uxQueueGetQueueNumber	1
../Middlewares/Third_Party/FreeRTOS/Source/queue.c:1994:7:vQueueSetQueueNumber	1
../Middlewares/Third_Party/FreeRTOS/Source/queue.c:2004:10:ucQueueGetQueueType	1
../Middlewares/Third_Party/FreeRTOS/Source/queue.c:2014:21:prvGetDisinheritPriorityAfterTimeout	2
../Middlewares/Third_Party/FreeRTOS/Source/queue.c:2039:19:prvCopyDataToQueue	8
../Middlewares/Third_Party/FreeRTOS/Source/queue.c:2118:13:prvCopyDataFromQueue	3
../Middlewares/Third_Party/FreeRTOS/Source/queue.c:2136:13:prvUnlockQueue	7
../Middlewares/Third_Party/FreeRTOS/Source/queue.c:2256:19:prvIsQueueEmpty	2
../Middlewares/Third_Party/FreeRTOS/Source/queue.c:2277:12:xQueueIsQueueEmptyFromISR	3
../Middlewares/Third_Party/FreeRTOS/Source/queue.c:2295:19:prvIsQueueFull	2
../Middlewares/Third_Party/FreeRTOS/Source/queue.c:2316:12:xQueueIsQueueFullFromISR	3
../Middlewares/Third_Party/FreeRTOS/Source/queue.c:2611:7:vQueueAddToRegistry	3
../Middlewares/Third_Party/FreeRTOS/Source/queue.c:2640:14:pcQueueGetName	3
../Middlewares/Third_Party/FreeRTOS/Source/queue.c:2668:7:vQueueUnregisterQueue	3
../Middlewares/Third_Party/FreeRTOS/Source/queue.c:2700:7:vQueueWaitForMessageRestricted	4
