../Middlewares/Third_Party/FreeRTOS/Source/tasks.c:575:15:xTaskCreateStatic	64	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/tasks.c:726:13:xTaskCreate	64	static
../Middlewares/Third_Party/FreeRTOS/Source/tasks.c:817:13:prvInitialiseNewTask	32	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/tasks.c:1021:13:prvAddNewTaskToReadyList	16	static
../Middlewares/Third_Party/FreeRTOS/Source/tasks.c:1106:7:vTaskDelete	24	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/tasks.c:1198:7:vTaskDelayUntil	32	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/tasks.c:1282:7:vTaskDelay	24	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/tasks.c:1327:13:eTaskGetState	32	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/tasks.c:1398:14:uxTaskPriorityGet	24	static
../Middlewares/Third_Party/FreeRTOS/Source/tasks.c:1420:14:uxTaskPriorityGetFromISR	32	static
../Middlewares/Third_Party/FreeRTOS/Source/tasks.c:1460:7:vTaskPrioritySet	32	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/tasks.c:1624:7:vTaskSuspend	24	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/tasks.c:1725:20:prvTaskIsTaskSuspended	24	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/tasks.c:1771:7:vTaskResume	24	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/tasks.c:1825:13:xTaskResumeFromISR	32	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/tasks.c:1895:6:vTaskStartScheduler	48	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/tasks.c:2015:6:vTaskEndScheduler	8	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/tasks.c:2026:6:vTaskSuspendAll	8	static
../Middlewares/Third_Party/FreeRTOS/Source/tasks.c:2099:12:xTaskResumeAll	24	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/tasks.c:2209:12:xTaskGetTickCount	16	static
../Middlewares/Third_Party/FreeRTOS/Source/tasks.c:2224:12:xTaskGetTickCountFromISR	16	static
../Middlewares/Third_Party/FreeRTOS/Source/tasks.c:2255:13:uxTaskGetNumberOfTasks	8	static
../Middlewares/Third_Party/FreeRTOS/Source/tasks.c:2263:7:pcTaskGetName	24	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/tasks.c:2403:14:uxTaskGetSystemState	32	static
../Middlewares/Third_Party/FreeRTOS/Source/tasks.c:2583:12:xTaskIncrementTick	32	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/tasks.c:2845:6:vTaskSwitchContext	16	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/tasks.c:2904:6:vTaskPlaceOnEventList	16	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/tasks.c:2921:6:vTaskPlaceOnUnorderedEventList	24	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/tasks.c:2947:7:vTaskPlaceOnEventListRestricted	24	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/tasks.c:2978:12:xTaskRemoveFromEventList	24	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/tasks.c:3046:6:vTaskRemoveFromUnorderedEventList	24	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/tasks.c:3080:6:vTaskSetTimeOutState	16	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/tasks.c:3092:6:vTaskInternalSetTimeOutState	16	static
../Middlewares/Third_Party/FreeRTOS/Source/tasks.c:3100:12:xTaskCheckForTimeOut	32	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/tasks.c:3163:6:vTaskMissedYield	8	static
../Middlewares/Third_Party/FreeRTOS/Source/tasks.c:3171:14:uxTaskGetTaskNumber	24	static
../Middlewares/Third_Party/FreeRTOS/Source/tasks.c:3194:7:vTaskSetTaskNumber	24	static
../Middlewares/Third_Party/FreeRTOS/Source/tasks.c:3218:8:prvIdleTask	16	static
../Middlewares/Third_Party/FreeRTOS/Source/tasks.c:3430:13:prvInitialiseTaskLists	16	static
../Middlewares/Third_Party/FreeRTOS/Source/tasks.c:3462:13:prvCheckTasksWaitingTermination	16	static
../Middlewares/Third_Party/FreeRTOS/Source/tasks.c:3493:7:vTaskGetInfo	32	static
../Middlewares/Third_Party/FreeRTOS/Source/tasks.c:3589:21:prvListTasksWithinSingleList	48	static
../Middlewares/Third_Party/FreeRTOS/Source/tasks.c:3622:18:prvTaskCheckFreeStackSpace	24	static
../Middlewares/Third_Party/FreeRTOS/Source/tasks.c:3642:14:uxTaskGetStackHighWaterMark	32	static
../Middlewares/Third_Party/FreeRTOS/Source/tasks.c:3670:14:prvDeleteTCB	16	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/tasks.c:3724:13:prvResetNextTaskUnblockTime	16	static
../Middlewares/Third_Party/FreeRTOS/Source/tasks.c:3750:15:xTaskGetCurrentTaskHandle	16	static
../Middlewares/Third_Party/FreeRTOS/Source/tasks.c:3767:13:xTaskGetSchedulerState	16	static
../Middlewares/Third_Party/FreeRTOS/Source/tasks.c:3795:13:xTaskPriorityInherit	24	static
../Middlewares/Third_Party/FreeRTOS/Source/tasks.c:3882:13:xTaskPriorityDisinherit	24	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/tasks.c:3962:7:vTaskPriorityDisinheritAfterTimeout	32	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/tasks.c:4376:12:uxTaskResetEventItemValue	16	static
../Middlewares/Third_Party/FreeRTOS/Source/tasks.c:4392:8:pvTaskIncrementMutexHeldCount	8	static
../Middlewares/Third_Party/FreeRTOS/Source/tasks.c:4409:11:ulTaskNotifyTake	24	static
../Middlewares/Third_Party/FreeRTOS/Source/tasks.c:4477:13:xTaskNotifyWait	32	static
../Middlewares/Third_Party/FreeRTOS/Source/tasks.c:4557:13:xTaskGenericNotify	40	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/tasks.c:4663:13:xTaskGenericNotifyFromISR	40	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/tasks.c:4787:7:vTaskNotifyGiveFromISR	32	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/tasks.c:4876:13:xTaskNotifyStateClear	24	static
../Middlewares/Third_Party/FreeRTOS/Source/tasks.c:4906:13:prvAddCurrentTaskToDelayedList	24	static
