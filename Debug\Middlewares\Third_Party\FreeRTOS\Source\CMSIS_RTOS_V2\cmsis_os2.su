../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.c:135:12:os<PERSON>ernelInitialize	24	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.c:156:12:osKernelGetInfo	24	static
../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.c:173:17:os<PERSON>ernelGetState	16	static
../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.c:198:12:osKernelStart	24	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.c:217:9:os<PERSON><PERSON>lLock	24	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.c:244:9:osKernelUnlock	24	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.c:276:9:osKernelRestoreLock	24	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.c:312:10:osKernelGetTickCount	24	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.c:324:10:osKernelGetTickFreq	8	static
../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.c:328:10:osKernelGetSysTimerCount	16	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.c:342:10:osKernelGetSysTimerFreq	8	static
../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.c:348:14:osThreadNew	80	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.c:414:13:osThreadGetName	32	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.c:427:14:osThreadGetId	24	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.c:439:17:osThreadGetState	32	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.c:461:10:osThreadGetStackSpace	32	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.c:474:10:osThreadGetStackSize	16	static
../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.c:484:12:osThreadSetPriority	32	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.c:502:14:osThreadGetPriority	32	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.c:515:12:osThreadYield	24	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.c:528:12:osThreadSuspend	32	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.c:546:12:osThreadResume	32	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.c:564:18:osThreadExit	8	static
../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.c:571:12:osThreadTerminate	48	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.c:600:10:osThreadGetCount	24	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.c:612:10:osThreadEnumerate	40	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.c:640:10:osThreadFlagsSet	48	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.c:668:10:osThreadFlagsClear	40	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.c:698:10:osThreadFlagsGet	24	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.c:716:10:osThreadFlagsWait	64	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.c:791:12:osDelay	32	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.c:808:12:osDelayUntil	32	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.c:827:13:TimerCallback	24	static
../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.c:837:13:osTimerNew	64	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.c:895:13:osTimerGetName	32	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.c:908:12:osTimerStart	40	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.c:929:12:osTimerStop	40	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.c:955:10:osTimerIsRunning	32	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.c:968:12:osTimerDelete	48	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.c:999:18:osEventFlagsNew	32	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.c:1035:10:osEventFlagsSet	40	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.c:1060:10:osEventFlagsClear	32	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.c:1081:10:osEventFlagsGet	32	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.c:1098:10:osEventFlagsWait	64	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.c:1148:12:osEventFlagsDelete	32	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.c:1172:13:osMutexNew	48	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.c:1251:12:osMutexAcquire	40	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.c:1292:12:osMutexRelease	40	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.c:1325:14:osMutexGetOwner	32	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.c:1340:12:osMutexDelete	32	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.c:1369:17:osSemaphoreNew	56	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.c:1436:12:osSemaphoreAcquire	40	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.c:1473:12:osSemaphoreRelease	40	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.c:1501:10:osSemaphoreGetCount	32	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.c:1517:12:osSemaphoreDelete	32	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.c:1545:20:osMessageQueueNew	64	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.c:1598:12:osMessageQueuePut	48	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.c:1639:12:osMessageQueueGet	48	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.c:1680:10:osMessageQueueGetCapacity	24	static
../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.c:1694:10:osMessageQueueGetMsgSize	24	static
../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.c:1708:10:osMessageQueueGetCount	32	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.c:1725:10:osMessageQueueGetSpace	40	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.c:1748:12:osMessageQueueReset	32	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.c:1766:12:osMessageQueueDelete	32	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.c:1857:6:vApplicationGetIdleTaskMemory	24	static
../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.c:1867:6:vApplicationGetTimerTaskMemory	24	static
