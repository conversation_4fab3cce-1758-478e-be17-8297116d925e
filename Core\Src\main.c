/* USER CODE BEGIN Header */
/**
  ******************************************************************************
  * @file           : main.c
  * @brief          : Main program body
  ******************************************************************************
  * @attention
  *
  * Copyright (c) 2025 STMicroelectronics.
  * All rights reserved.
  *
  * This software is licensed under terms that can be found in the LICENSE file
  * in the root directory of this software component.
  * If no LICENSE file comes with this software, it is provided AS-IS.
  *
  ******************************************************************************
  */
/* USER CODE END Header */
/* Includes ------------------------------------------------------------------*/
#include "main.h"
#include "cmsis_os.h"
#include "adc.h"
#include "dma.h"
#include "iwdg.h"
#include "tim.h"
#include "usart.h"
#include "gpio.h"

/* Private includes ----------------------------------------------------------*/
/* USER CODE BEGIN Includes */
#include <stdbool.h>
#include <string.h>
#include <stdio.h>
/* USER CODE END Includes */

/* Private typedef -----------------------------------------------------------*/
/* USER CODE BEGIN PTD */

/* USER CODE END PTD */

/* Private define ------------------------------------------------------------*/
/* USER CODE BEGIN PD */
bool TransmitMsgString(char* Buff){

	if(HAL_UART_Transmit(&huart2, (uint8_t*)Buff, strlen(Buff), 500)!=HAL_OK)
	{
		
		HAL_UART_AbortTransmit(&huart2);
		return false;
	}

	HAL_GPIO_TogglePin(LED_485_GPIO_Port,LED_485_Pin);
	return true;
}
/* USER CODE END PD */

/* Private macro -------------------------------------------------------------*/
/* USER CODE BEGIN PM */
char BufferSend[200];
uint32_t Data[8];
uint32_t PulseWidth[4];
/* USER CODE END PM */

/* Private variables ---------------------------------------------------------*/

/* USER CODE BEGIN PV */
extern unsigned int ADC_raw;

GPIO_PinState PinIn_State=GPIO_PIN_SET;

// Biến cho điều khiển PWM từ biến trở
uint32_t potentiometer_value = 0;
uint32_t pwm_duty_cycle = 0;
/* USER CODE END PV */

/* Private function prototypes -----------------------------------------------*/
void SystemClock_Config(void);
void MX_FREERTOS_Init(void);
/* USER CODE BEGIN PFP */

/* USER CODE END PFP */

/* Private user code ---------------------------------------------------------*/
/* USER CODE BEGIN 0 */
/**
 * @brief Chuyển đổi giá trị ADC thành duty cycle PWM
 * @param adc_value: Giá trị ADC (0-4095 cho 12-bit ADC)
 * @retval Duty cycle PWM (0-1280, tương ứng với period của TIM1)
 */
uint32_t ADC_to_PWM_DutyCycle(uint32_t adc_value)
{
    // Chuyển đổi từ 12-bit ADC (0-4095) sang PWM duty cycle (0-1280)
    // 1280 là giá trị Period của TIM1 (100% duty cycle)
    return (adc_value * 1280) / 4095;
}
/* USER CODE END 0 */

/**
  * @brief  The application entry point.
  * @retval int
  */
int main(void)
{

  /* USER CODE BEGIN 1 */

  /* USER CODE END 1 */

  /* MCU Configuration--------------------------------------------------------*/

  /* Reset of all peripherals, Initializes the Flash interface and the Systick. */
  HAL_Init();

  /* USER CODE BEGIN Init */

  /* USER CODE END Init */

  /* Configure the system clock */
  SystemClock_Config();

  /* USER CODE BEGIN SysInit */

  /* USER CODE END SysInit */

  /* Initialize all configured peripherals */
  MX_GPIO_Init();
  MX_DMA_Init();
  MX_TIM1_Init();
  MX_TIM3_Init();
  MX_USART1_UART_Init();
  MX_ADC_Init();
  MX_TIM17_Init();
  MX_USART2_UART_Init();
  MX_IWDG_Init();
  /* USER CODE BEGIN 2 */
HAL_TIM_PWM_Start(&htim1,TIM_CHANNEL_1);
HAL_TIM_PWM_Start(&htim1,TIM_CHANNEL_2);
HAL_TIM_PWM_Start(&htim1,TIM_CHANNEL_3);
HAL_TIM_PWM_Start(&htim1,TIM_CHANNEL_4);
// 32 Mhz : 1 pulse: 1/32 * x = 40 us
// x = 32*40 = 1280
// 25 khz: T periode = 40 us
__HAL_TIM_SET_COMPARE(&htim1,TIM_CHANNEL_1,0);
// TIM_CHANNEL_2 (PA9) sẽ được điều khiển bởi biến trở, khởi tạo = 0
__HAL_TIM_SET_COMPARE(&htim1,TIM_CHANNEL_2,0);
__HAL_TIM_SET_COMPARE(&htim1,TIM_CHANNEL_3,0);
__HAL_TIM_SET_COMPARE(&htim1,TIM_CHANNEL_4,0);

HAL_GPIO_WritePin(ADC_OUT_GPIO_Port,ADC_OUT_Pin,GPIO_PIN_SET);
  /* USER CODE END 2 */

  /* Init scheduler */
  osKernelInitialize();

  /* Call init function for freertos objects (in cmsis_os2.c) */
  MX_FREERTOS_Init();

  /* Start scheduler */
  osKernelStart();

  /* We should never get here as control is now taken by the scheduler */

  /* Infinite loop */
  /* USER CODE BEGIN WHILE */
  while (1)
  {
    /* USER CODE END WHILE */

    /* USER CODE BEGIN 3 */
				HAL_IWDG_Refresh(&hiwdg);
		
		HAL_Delay(1000);
			
//		if(Data[1]>Data[0]){
//			PulseWidth[0] = Data[1] - Data[0] - 1;
//		}else{
//			PulseWidth[0] = Data[1] + 65534 - Data[0];
//		}
//		if(PulseWidth[0] == 65534) PulseWidth[0]=0;
//		
//		if(Data[3]>Data[2]){
//			PulseWidth[1] = Data[3] - Data[2] - 1;
//		}else{
//			PulseWidth[1] = Data[3] + 65534 - Data[2];
//		}
//		if(PulseWidth[1] == 65534) PulseWidth[1]=0;
//		
//		if(Data[5]>Data[4]){
//			PulseWidth[2] = Data[5] - Data[4] - 1;
//		}else{
//			PulseWidth[2] = Data[5] + 65534 - Data[4];
//		}

//		if(PulseWidth[2] == 65534) PulseWidth[2]=0;
//		
//		if(Data[7]>Data[6]){
//			PulseWidth[3] = Data[7] - Data[6] - 1;
//		}else{
//			PulseWidth[3] = Data[7] + 65534 - Data[6];
//		}
//		if(PulseWidth[3] == 65534) PulseWidth[3]=0;
		
		// Đọc giá trị biến trở từ PA1 và điều khiển PWM ở PA9
		potentiometer_value = ADC_raw;  // Giá trị ADC từ PA1
		pwm_duty_cycle = ADC_to_PWM_DutyCycle(potentiometer_value);

		// Cập nhật PWM duty cycle cho TIM1_CH2 (PA9)
		__HAL_TIM_SET_COMPARE(&htim1, TIM_CHANNEL_2, pwm_duty_cycle);

		PinIn_State = HAL_GPIO_ReadPin(IN_1_GPIO_Port, IN_1_Pin);
		if(PinIn_State == GPIO_PIN_RESET){
			// Khi IN_1 được kích hoạt, bật các PWM channel khác (trừ CH2)
			__HAL_TIM_SET_COMPARE(&htim1,TIM_CHANNEL_1,640);  // 50% duty cycle
			// TIM_CHANNEL_2 (PA9) được điều khiển bởi biến trở
			__HAL_TIM_SET_COMPARE(&htim1,TIM_CHANNEL_3,640);  // 50% duty cycle
			__HAL_TIM_SET_COMPARE(&htim1,TIM_CHANNEL_4,640);  // 50% duty cycle
	}else{
			// Khi IN_1 không được kích hoạt, tắt các PWM channel khác (trừ CH2)
			__HAL_TIM_SET_COMPARE(&htim1,TIM_CHANNEL_1,0);
			// TIM_CHANNEL_2 (PA9) được điều khiển bởi biến trở
			__HAL_TIM_SET_COMPARE(&htim1,TIM_CHANNEL_3,0);
			__HAL_TIM_SET_COMPARE(&htim1,TIM_CHANNEL_4,0);
	}

		// Debug: Hiển thị thông tin biến trở và PWM
		sprintf(BufferSend,"Potentiometer: %lu, PWM_Duty: %lu\n",
				potentiometer_value, pwm_duty_cycle);
		TransmitMsgString(BufferSend);

//=======
//		if(Data[7]>Data[6]){
//			PulseWidth[3] = Data[7] - Data[6] - 1;
//		}else{
//			PulseWidth[3] = Data[7] + 65535 - Data[6] - 1;
//		}
		//sprintf(BufferSend,"{%d, %d, %d, %d}\n",PulseWidth[0],PulseWidth[1],PulseWidth[2],PulseWidth[3]);
		//	TransmitMsgString(BufferSend);

//		HAL_Delay(1000);
//		Data[0]=0;
//		Data[1]=0;
//		Data[2]=0;
//		Data[3]=0;
//		Data[4]=0;
//		Data[5]=0;
//		Data[6]=0;
//		Data[7]=0;
//		HAL_TIM_IC_Start_DMA(&htim3,TIM_CHANNEL_1,&Data[0],2);
//		HAL_TIM_IC_Start_DMA(&htim3,TIM_CHANNEL_3,&Data[2],2);
//		HAL_TIM_IC_Start_DMA(&htim3,TIM_CHANNEL_4,&Data[4],2);
//		HAL_TIM_IC_Start_DMA(&htim17,TIM_CHANNEL_1,&Data[6],2);
		
		HAL_ADC_Start_IT(&hadc);
  }
  /* USER CODE END 3 */
}

/**
  * @brief System Clock Configuration
  * @retval None
  */
void SystemClock_Config(void)
{
  RCC_OscInitTypeDef RCC_OscInitStruct = {0};
  RCC_ClkInitTypeDef RCC_ClkInitStruct = {0};
  RCC_PeriphCLKInitTypeDef PeriphClkInit = {0};

  /** Initializes the RCC Oscillators according to the specified parameters
  * in the RCC_OscInitTypeDef structure.
  */
  RCC_OscInitStruct.OscillatorType = RCC_OSCILLATORTYPE_HSI|RCC_OSCILLATORTYPE_HSI14
                              |RCC_OSCILLATORTYPE_LSI;
  RCC_OscInitStruct.HSIState = RCC_HSI_ON;
  RCC_OscInitStruct.HSI14State = RCC_HSI14_ON;
  RCC_OscInitStruct.HSICalibrationValue = RCC_HSICALIBRATION_DEFAULT;
  RCC_OscInitStruct.HSI14CalibrationValue = 16;
  RCC_OscInitStruct.LSIState = RCC_LSI_ON;
  RCC_OscInitStruct.PLL.PLLState = RCC_PLL_ON;
  RCC_OscInitStruct.PLL.PLLSource = RCC_PLLSOURCE_HSI;
  RCC_OscInitStruct.PLL.PLLMUL = RCC_PLL_MUL12;
  RCC_OscInitStruct.PLL.PREDIV = RCC_PREDIV_DIV1;
  if (HAL_RCC_OscConfig(&RCC_OscInitStruct) != HAL_OK)
  {
    Error_Handler();
  }

  /** Initializes the CPU, AHB and APB buses clocks
  */
  RCC_ClkInitStruct.ClockType = RCC_CLOCKTYPE_HCLK|RCC_CLOCKTYPE_SYSCLK
                              |RCC_CLOCKTYPE_PCLK1;
  RCC_ClkInitStruct.SYSCLKSource = RCC_SYSCLKSOURCE_PLLCLK;
  RCC_ClkInitStruct.AHBCLKDivider = RCC_SYSCLK_DIV1;
  RCC_ClkInitStruct.APB1CLKDivider = RCC_HCLK_DIV1;

  if (HAL_RCC_ClockConfig(&RCC_ClkInitStruct, FLASH_LATENCY_1) != HAL_OK)
  {
    Error_Handler();
  }
  PeriphClkInit.PeriphClockSelection = RCC_PERIPHCLK_USART1;
  PeriphClkInit.Usart1ClockSelection = RCC_USART1CLKSOURCE_PCLK1;
  if (HAL_RCCEx_PeriphCLKConfig(&PeriphClkInit) != HAL_OK)
  {
    Error_Handler();
  }
}

/* USER CODE BEGIN 4 */

/* USER CODE END 4 */

/**
  * @brief  This function is executed in case of error occurrence.
  * @retval None
  */
void Error_Handler(void)
{
  /* USER CODE BEGIN Error_Handler_Debug */
  /* User can add his own implementation to report the HAL error return state */
  __disable_irq();
  while (1)
  {
  }
  /* USER CODE END Error_Handler_Debug */
}

#ifdef  USE_FULL_ASSERT
/**
  * @brief  Reports the name of the source file and the source line number
  *         where the assert_param error has occurred.
  * @param  file: pointer to the source file name
  * @param  line: assert_param error line source number
  * @retval None
  */
void assert_failed(uint8_t *file, uint32_t line)
{
  /* USER CODE BEGIN 6 */
  /* User can add his own implementation to report the file name and line number,
     ex: printf("Wrong parameters value: file %s on line %d\r\n", file, line) */
  /* USER CODE END 6 */
}
#endif /* USE_FULL_ASSERT */
